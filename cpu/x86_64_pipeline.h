#pragma once

#include <cstdint>
#include <vector>
#include <memory>

// Forward declarations
namespace jit {
    enum class Status;
    class JitCache;
    class JitTranslator;
}

namespace memory {
    class MemoryManager;
}

namespace x86_64 {

class DecodedInstruction;
class X86_64CPU;

enum class ExecutionUnit {
    INTEGER,
    FLOATING_POINT,
    VECTOR,
    LOAD_STORE,
    ALU = INTEGER  // Alias for backward compatibility
};

enum class InstructionType {
    INTEGER_ALU,
    INTEGER_MUL,
    FLOAT_ADD,
    FLOAT_MUL,
    VECTOR_ADD,
    VECTOR_MUL,
    LOAD,
    STORE
};

// Basic pipeline stage structure
struct PipelineStage {
    DecodedInstruction instruction;
    uint64_t pc;
    int cycle;
};

// Detailed pipeline stage structures
struct FetchStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    bool predicted_taken;
    uint64_t predicted_target;
    uint64_t fetch_cycle;
    float prediction_confidence;
};

struct DecodeStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    bool predicted_taken;
    uint64_t predicted_target;
    uint64_t decode_cycle;
    uint64_t fetch_cycle;
    float prediction_confidence;
};

struct ExecuteStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    bool predicted_taken;
    uint64_t predicted_target;
    uint64_t execute_cycle;
    uint64_t fetch_cycle;
    float prediction_confidence;
    ExecutionUnit unit_type;
    int execution_latency;
};

struct MemoryStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    uint64_t memory_cycle;
    uint64_t fetch_cycle;
};

struct WriteBackStage {
    DecodedInstruction instr;
    uint64_t pc;
    bool valid;
    uint64_t writeback_cycle;
    uint64_t fetch_cycle;
};

class X86_64Pipeline {
private:
    memory::MemoryManager& memory_;
    jit::JitCache& jitCache_;
    jit::JitTranslator& translator_;
    int cpuId_;
    uint64_t pc_;

    std::vector<PipelineStage> executeStage_;
    std::vector<PipelineStage> memoryStage_;

public:
    X86_64Pipeline(memory::MemoryManager& memory,
                   jit::JitCache& cache,
                   jit::JitTranslator& translator,
                   int cpuId);

    bool translateAndCache(uint64_t pc);
    bool HasWAWHazard(const DecodedInstruction& instr);
    bool HasWARHazard(const DecodedInstruction& instr);
    ExecutionUnit GetRequiredExecutionUnit(const DecodedInstruction& instr);
    int GetInstructionLatency(const DecodedInstruction& instr);
};

} // namespace x86_64